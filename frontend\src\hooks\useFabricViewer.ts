import { useCallback, useRef, useState } from "react";
import { Canvas } from "fabric";
import { createSave<PERSON><PERSON><PERSON>, createShowOriginalHandler } from "@/lib/fabric/operations";
import { setupCanvasEventListeners } from "@/lib/fabric/events";
import {
  CropData,
  ImageViewerProps,
  FabricObjectState,
  TransformState,
  FilterParams,
} from "@/shared/types";
import { setupImageCanvas } from "@/lib/fabric/canvas";
import { useUndoTracking } from "./useUndoTracking";
import { useFilterManagement } from "./useFilterManagement";
import { useImageTransforms } from "./useImageTransforms";
import { useCropManagement } from "./useCropManagement";

export const useFabricViewer = ({
  data,
  containerRef,
  onResizeNeeded,
}: ImageViewerProps & {
  containerRef?: React.RefObject<HTMLElement | null>;
  onResizeNeeded?: () => void;
}) => {
  const fabricCanvas = useRef<Canvas | null>(null);
  const eventDisposers = useRef<(() => void)[]>([]);
  const initialObjectCount = useRef(0);
  const originalImageUrl = useRef("");
  const objectStates = useRef(new Map<string, FabricObjectState>());
  const [isShowingOriginal, setIsShowingOriginal] = useState(false);

  const undoTracking = useUndoTracking(fabricCanvas, initialObjectCount);

  const filterManagement = useFilterManagement(
    {
      brightness: data.viewer.fabricConfigs.brightness,
      contrast: data.viewer.fabricConfigs.contrast,
      grayscale: data.viewer.fabricConfigs.grayscale,
      invert: data.viewer.fabricConfigs.invert,
      sharpness: data.viewer.fabricConfigs.sharpness,
      gammaR: data.viewer.fabricConfigs.gammaR,
      gammaG: data.viewer.fabricConfigs.gammaG,
      gammaB: data.viewer.fabricConfigs.gammaB,
    },
    fabricCanvas
  );

  const imageTransforms = useImageTransforms(
    fabricCanvas,
    data.viewer.fabricConfigs.transformState || {
      rotations: 0,
      flipHorizontal: false,
      flipVertical: false,
    },
    onResizeNeeded
  );

  const cropManagement = useCropManagement(
    fabricCanvas,
    data.viewer.fabricConfigs.cropData || {
      isCropped: false,
      normalizedCropRect: undefined,
      canvasDimensions: undefined,
    },
    undoTracking,
    containerRef,
    originalImageUrl.current,
    imageTransforms.transformState
  );
  const isInitializingRef = useRef(false);
  const cachedState = useRef<{
    annotations: any;
    filters: any;
    cropData: CropData;
    transformState: TransformState;
  } | null>(null);

  const setupCanvas = useCallback(
    async (canvasElement: HTMLCanvasElement, imageSource: string) => {
      if (isInitializingRef.current) return;
      isInitializingRef.current = true;

      const result = await setupImageCanvas({
        canvasElement,
        imageUrl: imageSource,
        annotations: data.viewer.fabricConfigs.annotations,
        filters: filterManagement.filters as FilterParams,
        cropData: data.viewer.fabricConfigs.cropData as CropData,
        existingCanvas: fabricCanvas.current,
        transformState: data.viewer.fabricConfigs.transformState,
      });

      fabricCanvas.current = result.canvas;
      originalImageUrl.current = imageSource;

      if (data.viewer.fabricConfigs.cropData) {
        const loadedCropData = data.viewer.fabricConfigs.cropData as CropData;
        if (loadedCropData.isCropped !== cropManagement.cropData.isCropped) {
          cropManagement.setCropData(loadedCropData);
          cropManagement.setHasPerformedCrop(loadedCropData.isCropped);
        }
      }
      const canvas = fabricCanvas.current;
      initialObjectCount.current = canvas.getObjects().length;

      eventDisposers.current.forEach((dispose) => dispose());
      eventDisposers.current = setupCanvasEventListeners(canvas, undoTracking, objectStates);

      isInitializingRef.current = false;
    },
    [
      data.viewer.fabricConfigs.annotations,
      data.viewer.fabricConfigs.cropData,
      data.viewer.fabricConfigs.transformState,
      filterManagement.filters,
      undoTracking,
      cropManagement,
    ]
  );

  const handleSave = createSaveHandler(
    fabricCanvas,
    data.id,
    filterManagement.filters,
    cropManagement.cropData,
    imageTransforms.transformState,
    data.viewer.fabricConfigs.calibrationData
  );

  const handleShowOriginal = createShowOriginalHandler(
    fabricCanvas,
    isShowingOriginal,
    setIsShowingOriginal,
    undoTracking.isUndoingRef,
    cachedState,
    filterManagement.filters,
    cropManagement.cropData,
    imageTransforms.transformState,
    originalImageUrl,
    cropManagement.setCropData,
    imageTransforms.setTransformState,
    cropManagement.setHasPerformedCrop,
    containerRef
  );

  return {
    canvas: fabricCanvas,
    setupCanvas,
    ...filterManagement.filters,
    ...filterManagement.filterHandlers,
    ...imageTransforms,
    ...undoTracking,
    ...cropManagement,
    handleSave,
    handleShowOriginal,
    isShowingOriginal,
  };
};
