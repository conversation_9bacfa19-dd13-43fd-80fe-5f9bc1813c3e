import { useCallback, useState, useMemo } from "react";
import { Canvas } from "fabric";
import {
  createBrightnessHandler,
  create<PERSON>ontrastHand<PERSON>,
  create<PERSON>ray<PERSON>leHand<PERSON>,
  createInvertHand<PERSON>,
  create<PERSON>har<PERSON>ness<PERSON><PERSON><PERSON>,
  createGammaRHand<PERSON>,
  createGammaGHand<PERSON>,
  createGammaBHandler,
} from "@/lib/fabric/operations/filters";
import type { FilterState, FilterManagementState } from "@/shared/types";

export const useFilterManagement = (
  initialFilters: FilterState,
  fabricCanvas: React.RefObject<Canvas | null>
): FilterManagementState => {
  const [filters, setFilters] = useState<FilterState>(initialFilters);

  const updateFilter = useCallback((keyOrConfig: string | object, value?: number | boolean) => {
    if (typeof keyOrConfig === "object") {
      setFilters((prev) => ({ ...prev, ...keyOrConfig }));
    } else {
      setFilters((prev) => ({ ...prev, [keyOrConfig]: value }));
    }
  }, []);

  const filterHandlers = useMemo(() => {
    return {
      handleBrightnessChange: createBrightnessHandler(fabricCanvas, updateFilter),
      handleContrastChange: createContrastHandler(fabricCanvas, updateFilter),
      handleGrayscaleChange: createGrayscaleHandler(fabricCanvas, updateFilter),
      handleInvertChange: createInvertHandler(fabricCanvas, updateFilter),
      handleSharpnessChange: createSharpnessHandler(fabricCanvas, updateFilter),
      handleGammaRChange: createGammaRHandler(fabricCanvas, updateFilter),
      handleGammaGChange: createGammaGHandler(fabricCanvas, updateFilter),
      handleGammaBChange: createGammaBHandler(fabricCanvas, updateFilter),
    };
  }, [updateFilter, fabricCanvas]);

  return {
    filters,
    filterHandlers,
    updateFilter,
  };
};
