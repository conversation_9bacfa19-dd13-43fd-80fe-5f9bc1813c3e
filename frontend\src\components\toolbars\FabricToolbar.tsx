import React, { useState, useEffect } from "react";
import { ImageToolbarProps } from "@/shared/types";
import { useFabricTools } from "@/hooks/useFabricTools";
import {
  ToolGrid,
  TransformControls,
  SliderControls,
  GammaControls,
  ActionButtons,
  CalibrationPrompt,
} from "./components";
import CalibrationModal from "./components/CalibrationModal";
import { Rnd } from "react-rnd";
import {
  createCalibrationSubmitHandler,
  createCalibrationCloseHandler,
  getCalibrationFromLocalStorage,
  clearCalibrationFromLocalStorage,
} from "@/lib/fabric/operations/calibration";

const FabricToolbar: React.FC<ImageToolbarProps> = ({
  fabricCanvas,
  brightness,
  contrast,
  grayscale,
  invert,
  sharpness,
  gammaR,
  gammaG,
  gammaB,
  onBrightnessChange,
  onContrastChange,
  onGrayscaleChange,
  onInvertChange,
  onSharpnessChange,
  onGammaRChange,
  onGammaGChange,
  onGammaBChange,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onUndo,
  canUndo = false,
  onSave,
  onShowOriginal,
  onShapeCreated,
  onCrop,
  disableGrayscale = false,
  disableGamma = false,
  disableUndoTracking,
  enableUndoTracking,
  isShowingOriginal = false,
  hasPerformedCrop = false,
  calibrationData,
}) => {
  const [isCalibrationModalOpen, setCalibrationModalOpen] = useState(false);
  const [isCalibrationPromptOpen, setCalibrationPromptOpen] = useState(false);
  const [localCalibrationData, setLocalCalibrationData] = useState(() => {
    // Priority: backend data > localStorage > null
    return calibrationData || getCalibrationFromLocalStorage();
  });

  useEffect(() => {
    if (calibrationData) {
      setLocalCalibrationData(calibrationData);
    }
    return () => {
      clearCalibrationFromLocalStorage();
    };
  }, [calibrationData]);

  const { activeMode, changeToolMode } = useFabricTools({
    fabricCanvas,
    isShowingOriginal,
    hasPerformedCrop,
    onShapeCreated,
    onCrop,
    disableUndoTracking,
    enableUndoTracking,
    showCalibrationModal: () => setCalibrationModalOpen(true),
    calibrationData: localCalibrationData || undefined,
    onCalibrationPrompt: () => setCalibrationPromptOpen(true),
  });

  return (
    <>
      <Rnd
        default={{
          x: 0,
          y: window.innerHeight / 2 - 150,
          width: 175,
          height: 300,
        }}
        bounds=".viewer-panel"
        enableResizing={false}
      >
        <div className={`fabric-toolbar-vertical ${isShowingOriginal ? "disabled" : ""}`}>
          <div className="annotation-tools">
            <ToolGrid
              activeMode={activeMode}
              isShowingOriginal={isShowingOriginal}
              hasPerformedCrop={hasPerformedCrop}
              onToolSelect={changeToolMode}
              onCrop={onCrop}
            />
            <TransformControls
              isShowingOriginal={isShowingOriginal}
              grayscale={grayscale}
              invert={invert}
              disableGrayscale={disableGrayscale}
              onRotate={onRotate}
              onFlipHorizontal={onFlipHorizontal}
              onFlipVertical={onFlipVertical}
              onGrayscaleChange={onGrayscaleChange}
              onInvertChange={onInvertChange}
              onShowOriginal={onShowOriginal}
            />
          </div>
          <SliderControls
            brightness={brightness}
            contrast={contrast}
            sharpness={sharpness}
            isShowingOriginal={isShowingOriginal}
            onBrightnessChange={onBrightnessChange}
            onContrastChange={onContrastChange}
            onSharpnessChange={onSharpnessChange}
          />
          <GammaControls
            gammaR={gammaR}
            gammaG={gammaG}
            gammaB={gammaB}
            disableGamma={disableGamma}
            isShowingOriginal={isShowingOriginal}
            onGammaRChange={onGammaRChange}
            onGammaGChange={onGammaGChange}
            onGammaBChange={onGammaBChange}
          />
          <ActionButtons
            canUndo={canUndo}
            isShowingOriginal={isShowingOriginal}
            onUndo={onUndo}
            onSave={onSave}
          />
        </div>
      </Rnd>
      <CalibrationModal
        isOpen={isCalibrationModalOpen}
        onClose={createCalibrationCloseHandler(fabricCanvas?.current, setCalibrationModalOpen)}
        onSubmit={createCalibrationSubmitHandler(fabricCanvas?.current, () => {
          // Update local calibration data from localStorage
          const newCalibrationData = getCalibrationFromLocalStorage();
          setLocalCalibrationData(newCalibrationData);
          // Close the calibration modal
          setCalibrationModalOpen(false);
          // After calibration is complete, switch to measure mode
          setTimeout(() => changeToolMode("measure"), 100);
        })}
      />
      <CalibrationPrompt
        isOpen={isCalibrationPromptOpen}
        onClose={() => setCalibrationPromptOpen(false)}
        onCalibrate={() => {
          setCalibrationPromptOpen(false);
          changeToolMode("calibrate");
        }}
      />
    </>
  );
};

export default FabricToolbar;
