import { Canvas, FabricImage, filters } from "fabric";
import { FilterParams, PartialFilterParams } from "@/shared/types";
import { defaultFabricConfigs } from "@/config/defaultFabricConfigs";

export const canvasFilterStates = new Map<Canvas, FilterParams>();

export const applyCanvasFilters = (canvas: Canvas, partialFilters: PartialFilterParams): void => {
  let currentState = canvasFilterStates.get(canvas);
  if (!currentState) {
    currentState = { ...defaultFabricConfigs };
    canvasFilterStates.set(canvas, currentState);
  }
  const newState = { ...currentState, ...partialFilters };
  canvasFilterStates.set(canvas, newState);
  const filterArray: any[] = [];
  if (newState.brightness !== 1) {
    filterArray.push(new filters.Brightness({ brightness: newState.brightness - 1 }));
  }
  if (newState.contrast !== 1) {
    filterArray.push(new filters.Contrast({ contrast: newState.contrast - 1 }));
  }
  if (newState.grayscale) {
    filterArray.push(new filters.Grayscale());
  }
  if (newState.invert) {
    filterArray.push(new filters.Invert());
  }
  if (newState.sharpness !== 1) {
    const matrix =
      newState.sharpness > 1
        ? [0, -1, 0, -1, 5, -1, 0, -1, 0]
        : [1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9];
    filterArray.push(new filters.Convolute({ matrix }));
  }
  if (newState.gammaR !== 1 || newState.gammaG !== 1 || newState.gammaB !== 1) {
    filterArray.push(
      new filters.Gamma({
        gamma: [newState.gammaR, newState.gammaG, newState.gammaB],
      })
    );
  }
  canvas.forEachObject((obj) => {
    if (obj instanceof FabricImage) {
      (obj as any).filters = [...filterArray];
      obj.applyFilters();
    }
  });
  if (canvas.backgroundImage instanceof FabricImage) {
    (canvas.backgroundImage as any).filters = [...filterArray];
    canvas.backgroundImage.applyFilters();
  }
  canvas.renderAll();
};

export const createBrightnessHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  updateFilter: (key: string, value: number) => void
) => {
  return (value: number) => {
    updateFilter("brightness", value);
    if (fabricCanvas.current) applyCanvasFilters(fabricCanvas.current, { brightness: value });
  };
};

export const createContrastHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  updateFilter: (key: string, value: number) => void
) => {
  return (value: number) => {
    updateFilter("contrast", value);
    if (fabricCanvas.current) applyCanvasFilters(fabricCanvas.current, { contrast: value });
  };
};

export const createGrayscaleHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  updateFilter: (key: string, value: boolean) => void
) => {
  return (value: boolean) => {
    updateFilter("grayscale", value);
    if (fabricCanvas.current) applyCanvasFilters(fabricCanvas.current, { grayscale: value });
  };
};

export const createInvertHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  updateFilter: (key: string, value: boolean) => void
) => {
  return (value: boolean) => {
    updateFilter("invert", value);
    if (fabricCanvas.current) applyCanvasFilters(fabricCanvas.current, { invert: value });
  };
};

export const createSharpnessHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  updateFilter: (key: string, value: number) => void
) => {
  return (value: number) => {
    updateFilter("sharpness", value);
    if (fabricCanvas.current) applyCanvasFilters(fabricCanvas.current, { sharpness: value });
  };
};

export const createGammaRHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  updateFilter: (key: string, value: number) => void
) => {
  return (value: number) => {
    updateFilter("gammaR", value);
    if (fabricCanvas.current) applyCanvasFilters(fabricCanvas.current, { gammaR: value });
  };
};

export const createGammaGHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  updateFilter: (key: string, value: number) => void
) => {
  return (value: number) => {
    updateFilter("gammaG", value);
    if (fabricCanvas.current) applyCanvasFilters(fabricCanvas.current, { gammaG: value });
  };
};

export const createGammaBHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  updateFilter: (key: string, value: number) => void
) => {
  return (value: number) => {
    updateFilter("gammaB", value);
    if (fabricCanvas.current) applyCanvasFilters(fabricCanvas.current, { gammaB: value });
  };
};
