import { Canvas } from "fabric";
import { FilterParams, SetupCanvasParams } from "@/shared/types";
import { loadAnnotations } from "../operations/annotations";
import { applyCanvasFilters } from "../operations/filters";
import { loadCanvasImage } from "../rendering/image";
import { createImageLoadContainer, applyCropToCanvas } from "../operations/crop";
import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "../operations/transforms";



const canvasFilterStates = new Map<Canvas, FilterParams>();

// Creates and configures an image canvas with image, filters, and annotations
export const setupImageCanvas = async ({
  canvasElement,
  imageUrl,
  annotations,
  filters,
  cropData,
  existingCanvas,
  transformState,
}: SetupCanvasParams): Promise<{
  canvas: Canvas;
}> => {
  const finalImageSource = imageUrl;

  // Apply saved transforms
  const applyTransforms = () => {
    if (transformState?.rotations) {
      for (let i = 0; i < transformState.rotations; i++) {
        applyCanvasRotation(canvas);
      }
    }
    if (transformState?.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas);
    }
    if (transformState?.flipVertical) {
      applyCanvasFlipVertical(canvas);
    }
  };
  if (existingCanvas) {
    existingCanvas.dispose();
  }

  const canvas = new Canvas(canvasElement, {
    selection: true,
    backgroundColor: "transparent",
  });

  const containerRect = canvasElement.parentElement?.getBoundingClientRect();

  if (cropData?.isCropped && cropData.normalizedCropRect) {
    const imageLoadContainer = createImageLoadContainer(cropData, containerRect);

    await loadCanvasImage(canvas, finalImageSource, {
      containerRect: imageLoadContainer || undefined,
    });

    // Apply operations in the correct order based on when crop was performed
    const transformsAtCrop = cropData.transformStateAtCrop;
    const currentTransforms = transformState;

    if (transformsAtCrop) {
      // Apply transforms that existed when crop was performed
      if (transformsAtCrop.rotations) {
        for (let i = 0; i < transformsAtCrop.rotations; i++) {
          applyCanvasRotation(canvas);
        }
      }
      if (transformsAtCrop.flipHorizontal) {
        applyCanvasFlipHorizontal(canvas);
      }
      if (transformsAtCrop.flipVertical) {
        applyCanvasFlipVertical(canvas);
      }
    }

    // Apply crop
    await applyCropToCanvas(canvas, cropData);

    // Apply any additional transforms that were performed after crop
    if (currentTransforms && transformsAtCrop) {
      const additionalRotations =
        (currentTransforms.rotations - transformsAtCrop.rotations + 4) % 4;
      for (let i = 0; i < additionalRotations; i++) {
        applyCanvasRotation(canvas);
      }
      if (currentTransforms.flipHorizontal !== transformsAtCrop.flipHorizontal) {
        applyCanvasFlipHorizontal(canvas);
      }
      if (currentTransforms.flipVertical !== transformsAtCrop.flipVertical) {
        applyCanvasFlipVertical(canvas);
      }
    }
  } else {
    await loadCanvasImage(canvas, finalImageSource, {
      containerRect: containerRect || undefined,
    });
    applyTransforms();
  }

  // Apply filters if provided
  if (filters) {
    canvasFilterStates.set(canvas, { ...filters });
    canvas.renderAll();
    applyCanvasFilters(canvas, filters);
  }

  // Load annotations if provided
  if (annotations) {
    await loadAnnotations(canvas, annotations);
  }

  // Configure canvas for viewer mode (non-interactive annotations)
  canvas.selection = false;
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  return {
    canvas,
  };
};
